{"rustc": 6160676876980946577, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 2040997289075261528, "path": 2520442750682607784, "deps": [[10411997081178400487, "cfg_if", false, 16108055597910833855]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\getrandom-5b4a6cd00daa6217\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
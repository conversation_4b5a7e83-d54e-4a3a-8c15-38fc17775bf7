[1750995766.659442] [WARN]   Logger initialized successfully
[1750995766.659739] [INFO]   === Koneko Rust Implementation Starting ===
[1750995766.660137] [INFO]   !!! Sandbox checks disabled via command-line flag !!!
[1750995766.660359] [INFO]   !!! This is for testing purposes only !!!
[1750995766.660752] [INFO]   Initializing global variables
[1750995766.661142] [INFO]   Collecting call r12 gadgets
[1750995766.764299] [INFO]   Skipping sandbox/VM check (disabled via command-line flag)
[1750995766.764844] [INFO]   Starting main functionality
[1750995766.765045] [INFO]   Entering run_me() function
[1750995766.765481] [INFO]   Skipping KUSER_SHARED_DATA checks (disabled via command-line flag)
[1750995766.765775] [INFO]   Skipping VDLL / Defender emulator check (disabled via command-line flag)
[1750995766.765994] [INFO]   Skipping debugger detection (disabled via command-line flag)
[1750995766.766242] [INFO]   Starting shellcode deobfuscation and preparation
[1750995766.766403] [INFO]   Deobfuscating shellcode using the original Koneko approach
[1750995766.766692] [INFO]   Shellcode deobfuscation complete: 392 bytes
[1750995766.767021] [INFO]   Allocating memory for shellcode
[1750995766.845402] [INFO]   Using NtAllocateVirtualMemory for shellcode allocation
[1750995766.845820] [INFO]   Verifying memory protection flags after allocation for 100% fidelity with original Koneko C++ implementation
[1750995766.931998] [ERROR]  ❌ Memory does NOT have PAGE_EXECUTE_READWRITE protection! Current protection: 0x0
[1750995766.932855] [ERROR]  This does not match the original Koneko C++ implementation which requires PAGE_EXECUTE_READWRITE (0x40)
[1750995766.932985] [INFO]   Writing shellcode to allocated memory
[1750995766.933303] [INFO]   Using WriteProcessMemory for direct byte-for-byte copying of shellcode
[1750995766.933769] [INFO]   Writing shellcode even with sandbox checks disabled
[1750995766.934130] [INFO]   Writing shellcode with WriteProcessMemory for 100% fidelity
[1750995766.934669] [ERROR]  WriteProcessMemory failed with error code: 998

C:\Users\<USER>\Documents\a\rust_reimplementation\src\target\release\librust_indirect_syscalls.rlib: C:\Users\<USER>\Documents\a\rust_reimplementation\src\build.rs C:\Users\<USER>\Documents\a\rust_reimplementation\src\src/asm/callme.asm C:\Users\<USER>\Documents\a\rust_reimplementation\src\src/asm/callr12.asm C:\Users\<USER>\Documents\a\rust_reimplementation\src\src/asm/spoof.asm C:\Users\<USER>\Documents\a\rust_reimplementation\src\src\hde64.rs C:\Users\<USER>\Documents\a\rust_reimplementation\src\src\improved_return_address_finder.rs C:\Users\<USER>\Documents\a\rust_reimplementation\src\src\lib.rs C:\Users\<USER>\Documents\a\rust_reimplementation\src\src\logger.rs
